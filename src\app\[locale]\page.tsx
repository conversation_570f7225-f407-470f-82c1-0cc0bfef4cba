
import { LoginForm } from '@/components/auth/login-form';
import Image from 'next/image';
import { getScopedI18n } from '@/lib/i18n/server';
import type { Locale } from '@/lib/i18n/config';

export default async function LoginPage({ params }: { params: Promise<{ locale: Locale }> }) {
  // استخدام params.locale بشكل صريح، على الرغم من أن getScopedI18n قد يستخدم اللغة الحالية افتراضيًا
  const { locale } = await params;
  const t = await getScopedI18n(locale, 'login');
  const currentYear = new Date().getFullYear();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-secondary p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <Image 
            src="/logo-placeholder.svg" 
            alt={t('logoAlt')}
            width={100} 
            height={100} 
            className="mx-auto mb-4 rounded-full shadow-md"
            data-ai-hint="مزامنة حديثة"
          />
          <h1 className="text-3xl font-bold text-primary">{t('appName')}</h1>
          <p className="mt-2 text-muted-foreground">
            {t('tagline')}
          </p>
        </div>
        <LoginForm />
        <p className="mt-10 text-center text-sm text-muted-foreground">
          {t('copyright', { year: currentYear })}
        </p>
      </div>
    </div>
  );
}

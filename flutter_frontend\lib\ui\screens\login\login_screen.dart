import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/biometric_service.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  String email = '', password = '';
  bool loading = false, error = false, biometricLoading = false;
  bool biometricAvailable = false, biometricEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkBiometricStatus();
  }

  void _checkBiometricStatus() async {
    final status = await BiometricService.getBiometricStatus();
    if (mounted) {
      setState(() {
        biometricAvailable = status['isAvailable'] ?? false;
        biometricEnabled = status['isEnabled'] ?? false;
      });
    }
  }

  void _login() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() {
      loading = true;
      error = false;
    });

    bool success = await AuthService.login(email, password);

    if (mounted) {
      setState(() {
        loading = false;
        error = !success;
      });

      if (success) {
        // انتقل للوحة التحكم أو الصفحة الرئيسية
        // Navigator.pushReplacementNamed(context, '/dashboard');
      }
    }
  }

  void _biometricLogin() async {
    setState(() {
      biometricLoading = true;
      error = false;
    });

    try {
      final userId = await BiometricService.authenticateWithBiometric();
      if (userId != null) {
        // تسجيل دخول ناجح بالبصمة
        // يمكنك هنا استدعاء خدمة المصادقة مع معرف المستخدم
        // Navigator.pushReplacementNamed(context, '/dashboard');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تسجيل الدخول بنجاح بالبصمة')),
          );
        }
      } else {
        if (mounted) {
          setState(() { error = true; });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() { error = true; });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BiometricService.getLocalizedErrorMessage(e.toString()))),
        );
      }
    } finally {
      if (mounted) {
        setState(() { biometricLoading = false; });
      }
    }
  }

  void _setupBiometric() async {
    if (!biometricAvailable) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('البصمة غير متوفرة على هذا الجهاز')),
      );
      return;
    }

    // يجب أن يكون المستخدم مسجل دخول أولاً لتفعيل البصمة
    if (email.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال البريد الإلكتروني أولاً')),
      );
      return;
    }

    final success = await BiometricService.enableBiometric(email, email);
    if (mounted) {
      if (success) {
        setState(() { biometricEnabled = true; });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تفعيل البصمة بنجاح')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في تفعيل البصمة')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Logo
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: const Icon(
                      Icons.store,
                      size: 50,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Title
                  Text(
                    'تسجيل الدخول',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Email Field
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      prefixIcon: Icon(Icons.email),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (v) => email = v,
                    validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                  ),
                  const SizedBox(height: 16),

                  // Password Field
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور',
                      prefixIcon: Icon(Icons.lock),
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    onChanged: (v) => password = v,
                    validator: (v) => v!.isEmpty ? 'مطلوب' : null,
                  ),
                  const SizedBox(height: 16),

                  // Error Message
                  if (error)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade700, size: 20),
                          const SizedBox(width: 8),
                          const Text(
                            'بيانات غير صحيحة',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 16),

                  // Login Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: loading || biometricLoading ? null : _login,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: loading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('دخول', style: TextStyle(fontSize: 16)),
                    ),
                  ),

                  // Biometric Login Section
                  if (biometricAvailable) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 16),

                    // Biometric Login Button
                    if (biometricEnabled)
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: loading || biometricLoading ? null : _biometricLogin,
                          icon: biometricLoading
                              ? const SizedBox(
                                  height: 16,
                                  width: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.fingerprint),
                          label: const Text('تسجيل الدخول بالبصمة'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                    // Setup Biometric Button
                    if (!biometricEnabled)
                      SizedBox(
                        width: double.infinity,
                        child: TextButton.icon(
                          onPressed: _setupBiometric,
                          icon: const Icon(Icons.fingerprint),
                          label: const Text('تفعيل البصمة'),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                  ],

                  // Biometric Status Info
                  if (biometricAvailable) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue.shade700, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              biometricEnabled
                                  ? 'البصمة مفعلة - يمكنك تسجيل الدخول بسرعة'
                                  : 'يمكنك تفعيل البصمة لتسجيل دخول أسرع',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth-context";
import { Loader2, Fingerprint, Settings } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useScopedI18n } from "@/lib/i18n/client"; // For Client Components
import { BiometricSetup } from "./biometric-setup";
import {
  isBiometricSupported,
  hasBiometricCredentials,
  getBiometricAuthOptions,
  authenticateBiometric,
  verifyBiometricAuth
} from "@/lib/biometric-auth";
import { useState, useEffect } from "react";

export function LoginForm() {
  const { login, isLoading, biometricLogin } = useAuth();
  const t = useScopedI18n('login');
  const tAuth = useScopedI18n('auth'); // For auth related messages like "Login Failed"

  const [isBiometricSetupOpen, setIsBiometricSetupOpen] = useState(false);
  const [canUseBiometric, setCanUseBiometric] = useState(false);
  const [isBiometricLoading, setIsBiometricLoading] = useState(false);
  const [lastUsername, setLastUsername] = useState('');
  const [isBiometricSupportedState, setIsBiometricSupportedState] = useState(false);
  const [isClient, setIsClient] = useState(false);

  const loginFormSchema = z.object({
    username: z.string().min(1, { message: t('usernameRequired') }),
    password: z.string().min(1, { message: t('passwordRequired') }),
  });
  
  type LoginFormValues = z.infer<typeof loginFormSchema>;

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  useEffect(() => {
    // Set client state to true after component mounts
    setIsClient(true);

    // Check if biometric is available and user has credentials
    const checkBiometric = async () => {
      const supported = isBiometricSupported();
      setIsBiometricSupportedState(supported);

      const storedUsername = localStorage.getItem('lastUsername');

      if (supported && storedUsername) {
        const hasCredentials = hasBiometricCredentials(storedUsername);
        setCanUseBiometric(hasCredentials);
        setLastUsername(storedUsername);

        if (hasCredentials) {
          form.setValue('username', storedUsername);
        }
      }
    };

    checkBiometric();
  }, [form]);

  async function onSubmit(values: LoginFormValues) {
    // Store username for biometric use
    localStorage.setItem('lastUsername', values.username);

    // Login function in useAuth already handles toast messages.
    // If specific login failed messages were to be shown here, use tAuth.
    await login(values.username, values.password);
  }

  const handleBiometricLogin = async () => {
    if (!lastUsername) return;

    setIsBiometricLoading(true);
    try {
      const allowCredentials = getBiometricAuthOptions(lastUsername);
      if (allowCredentials.length === 0) {
        throw new Error('لا توجد بصمات مسجلة');
      }

      const authResponse = await authenticateBiometric(allowCredentials);
      const isValid = await verifyBiometricAuth(lastUsername, authResponse);

      if (isValid && biometricLogin) {
        await biometricLogin(lastUsername);
      } else {
        throw new Error('فشل في التحقق من البصمة');
      }
    } catch (error) {
      console.error('Biometric login failed:', error);
      // Handle error silently or show a toast
    } finally {
      setIsBiometricLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-sm shadow-xl">
      <CardHeader>
        <CardTitle className="text-2xl font-semibold text-center text-primary">{t('welcome')}</CardTitle>
        <CardDescription className="text-center">
          {t('signInPrompt')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('usernameLabel')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('usernamePlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('passwordLabel')}</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder={t('passwordPlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="space-y-3">
              <Button type="submit" className="w-full" disabled={isLoading || isBiometricLoading} variant="default">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('signingIn')}
                  </>
                ) : (
                  t('signInButton')
                )}
              </Button>

              {/* Biometric Login Button */}
              {canUseBiometric && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={handleBiometricLogin}
                  disabled={isLoading || isBiometricLoading}
                >
                  {isBiometricLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جارٍ التحقق من البصمة...
                    </>
                  ) : (
                    <>
                      <Fingerprint className="mr-2 h-4 w-4" />
                      تسجيل الدخول بالبصمة
                    </>
                  )}
                </Button>
              )}

              {/* Biometric Setup Button */}
              {isClient && isBiometricSupportedState && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="w-full"
                  onClick={() => setIsBiometricSetupOpen(true)}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  إعداد البصمة
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>

      {/* Biometric Setup Dialog */}
      <BiometricSetup
        isOpen={isBiometricSetupOpen}
        onClose={() => setIsBiometricSetupOpen(false)}
      />
    </Card>
  );
}
